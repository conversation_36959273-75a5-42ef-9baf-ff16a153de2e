<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="店铺ID">
              <j-search-select-tag placeholder="请选择店铺ID" v-model="queryParam.storeManageId" dict="store_manage where del_flag=0,store_name,id"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('店铺等级变更及奖励日志表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <store-level-change-logs-modal ref="modalForm" @ok="modalFormOk"></store-level-change-logs-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import StoreLevelChangeLogsModal from './modules/StoreLevelChangeLogsModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'StoreLevelChangeLogsList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      StoreLevelChangeLogsModal
    },
    data () {
      return {
        description: '店铺等级变更及奖励日志表管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'店铺ID',
            align:"center",
            dataIndex: 'storeManageId_dictText'
          },
          {
            title:'评定业绩周期 YYYY-MM',
            align:"center",
            dataIndex: 'assessmentPeriod'
          },
          {
            title:'变更前等级',
            align:"center",
            dataIndex: 'previousLevel_dictText'
          },
          {
            title:'变更后等级',
            align:"center",
            dataIndex: 'currentLevel_dictText'
          },
          {
            title:'评定使用的上月业绩金额',
            align:"center",
            dataIndex: 'assessedSales'
          },
          {
            title:'评定使用的合作企业家数量',
            align:"center",
            dataIndex: 'assessedEntrepreneurCount'
          },
          {
            title:'变更类型: UPGRADE, DOWNGRADE, MAINTAIN',
            align:"center",
            dataIndex: 'changeType_dictText'
          },
          {
            title:'发放的奖励金额',
            align:"center",
            dataIndex: 'rewardBalanceAmountGiven'
          },
          {
            title:'备注信息',
            align:"center",
            dataIndex: 'remarks'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/storeLevelChangeLogs/storeLevelChangeLogs/list",
          delete: "/storeLevelChangeLogs/storeLevelChangeLogs/delete",
          deleteBatch: "/storeLevelChangeLogs/storeLevelChangeLogs/deleteBatch",
          exportXlsUrl: "/storeLevelChangeLogs/storeLevelChangeLogs/exportXls",
          importExcelUrl: "storeLevelChangeLogs/storeLevelChangeLogs/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'sel_search',value:'storeManageId',text:'店铺ID',dictTable:"store_manage where del_flag=0", dictText:'store_name', dictCode:'id'})
        fieldList.push({type:'string',value:'assessmentPeriod',text:'评定业绩周期 YYYY-MM',dictCode:''})
        fieldList.push({type:'string',value:'previousLevel',text:'变更前等级',dictCode:'store_level'})
        fieldList.push({type:'string',value:'currentLevel',text:'变更后等级',dictCode:'store_level'})
        fieldList.push({type:'BigDecimal',value:'assessedSales',text:'评定使用的上月业绩金额',dictCode:''})
        fieldList.push({type:'int',value:'assessedEntrepreneurCount',text:'评定使用的合作企业家数量',dictCode:''})
        fieldList.push({type:'string',value:'changeType',text:'变更类型: UPGRADE, DOWNGRADE, MAINTAIN',dictCode:'store_level_change_type'})
        fieldList.push({type:'BigDecimal',value:'rewardBalanceAmountGiven',text:'发放的奖励金额',dictCode:''})
        fieldList.push({type:'string',value:'remarks',text:'备注信息',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>