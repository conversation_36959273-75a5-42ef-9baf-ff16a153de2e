-- 注意：该页面对应的前台目录为views/storeLevelChangeLogs文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2025052611234870000', NULL, '店铺等级变更及奖励日志表', '/storeLevelChangeLogs/storeLevelChangeLogsList', 'storeLevelChangeLogs/StoreLevelChangeLogsList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, 0, 0, 0, 0, NULL, '1', 0, 0, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880001', '2025052611234870000', '添加店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:add', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880002', '2025052611234870000', '编辑店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880003', '2025052611234870000', '删除店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880004', '2025052611234870000', '批量删除店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:deleteBatch', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880005', '2025052611234870000', '导出excel_店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:exportXls', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025052611234880006', '2025052611234870000', '导入excel_店铺等级变更及奖励日志表', NULL, NULL, 0, NULL, NULL, 2, 'storeLevelChangeLogs:store_level_change_logs:importExcel', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-05-26 23:23:00', NULL, NULL, 0, 0, '1', 0);