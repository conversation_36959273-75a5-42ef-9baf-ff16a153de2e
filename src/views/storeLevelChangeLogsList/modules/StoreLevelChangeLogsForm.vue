<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="评定业绩周期 YYYY-MM" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="assessmentPeriod">
              <a-input v-model="model.assessmentPeriod" placeholder="请输入评定业绩周期 YYYY-MM"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="变更前等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="previousLevel">
              <j-dict-select-tag type="list" v-model="model.previousLevel" dictCode="store_level" placeholder="请选择变更前等级" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="变更后等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="currentLevel">
              <j-dict-select-tag type="list" v-model="model.currentLevel" dictCode="store_level" placeholder="请选择变更后等级" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评定使用的上月业绩金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="assessedSales">
              <a-input-number v-model="model.assessedSales" placeholder="请输入评定使用的上月业绩金额" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评定使用的合作企业家数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="assessedEntrepreneurCount">
              <a-input-number v-model="model.assessedEntrepreneurCount" placeholder="请输入评定使用的合作企业家数量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="变更类型: UPGRADE, DOWNGRADE, MAINTAIN" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="changeType">
              <j-dict-select-tag type="list" v-model="model.changeType" dictCode="store_level_change_type" placeholder="请选择变更类型: UPGRADE, DOWNGRADE, MAINTAIN" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发放的奖励金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="rewardBalanceAmountGiven">
              <a-input-number v-model="model.rewardBalanceAmountGiven" placeholder="请输入发放的奖励金额" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注信息" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remarks">
              <a-textarea v-model="model.remarks" rows="4" placeholder="请输入备注信息" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StoreLevelChangeLogsForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           assessmentPeriod: [
              { required: true, message: '请输入评定业绩周期 YYYY-MM!'},
           ],
           previousLevel: [
              { required: true, message: '请输入变更前等级!'},
           ],
           currentLevel: [
              { required: true, message: '请输入变更后等级!'},
           ],
           assessedSales: [
              { required: false},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
           assessedEntrepreneurCount: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           changeType: [
              { required: true, message: '请输入变更类型: UPGRADE, DOWNGRADE, MAINTAIN!'},
           ],
           rewardBalanceAmountGiven: [
              { required: false},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
        },
        url: {
          add: "/storeLevelChangeLogs/storeLevelChangeLogs/add",
          edit: "/storeLevelChangeLogs/storeLevelChangeLogs/edit",
          queryById: "/storeLevelChangeLogs/storeLevelChangeLogs/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>