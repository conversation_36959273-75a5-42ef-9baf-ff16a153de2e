<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="店铺等级值" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="levelValue">
              <j-dict-select-tag type="list" v-model="model.levelValue" dictCode="store_level" placeholder="请选择店铺等级值" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="店铺等级名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="levelName">
              <a-input v-model="model.levelName" placeholder="请输入店铺等级名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="最低月业绩要求" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="minMonthlySales">
              <a-input-number v-model="model.minMonthlySales" placeholder="请输入最低月业绩要求" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="最低关联企业家数量要求" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="minEntrepreneurCount">
              <a-input-number v-model="model.minEntrepreneurCount" placeholder="请输入最低关联企业家数量要求" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="月度奖励金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="monthlyRewardBalanceAmount">
              <a-input-number v-model="model.monthlyRewardBalanceAmount" placeholder="请输入月度奖励金额" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="等级说明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-textarea v-model="model.description" rows="4" placeholder="请输入等级说明" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="排序字段" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sortOrder">
              <a-input-number v-model="model.sortOrder" placeholder="请输入排序字段" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否启用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isActive">
              <j-switch v-model="model.isActive"  ></j-switch>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StoreLevelConfigurationsForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
            isActive:1,
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           levelValue: [
              { required: true, message: '请输入店铺等级值!'},
           ],
           levelName: [
              { required: true, message: '请输入店铺等级名称!'},
           ],
           minMonthlySales: [
              { required: true, message: '请输入最低月业绩要求!'},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
           minEntrepreneurCount: [
              { required: true, message: '请输入最低关联企业家数量要求!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           monthlyRewardBalanceAmount: [
              { required: false},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
           sortOrder: [
              { required: false},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
        },
        url: {
          add: "/storeLevelConfigurations/storeLevelConfigurations/add",
          edit: "/storeLevelConfigurations/storeLevelConfigurations/edit",
          queryById: "/storeLevelConfigurations/storeLevelConfigurations/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>